import pytest
from unittest.mock import Mock, patch
import json
from trader.mercado_bitcoin_api import MercadoBitcoinAPI

class TestMercadoBitcoinAPI:
    
    @pytest.fixture
    def api(self):
        """Instância da API para testes"""
        return MercadoBitcoinAPI("test_key", "test_secret")
    
    def test_init(self):
        """Testa inicialização da API"""
        api = MercadoBitcoinAPI("my_key", "my_secret")
        
        assert api.api_key == "my_key"
        assert api.api_secret == "my_secret"
        assert api.base_url == "https://api.mercadobitcoin.net/api/v4"
    
    @patch('time.time')
    def test_generate_signature(self, mock_time, api):
        """Testa geração de assinatura"""
        mock_time.return_value = 1640995200.123  # Timestamp fixo
        
        timestamp, signature = api._generate_signature("GET", "/api/v4/test", "")
        
        assert timestamp == "1640995200123"
        assert isinstance(signature, str)
        assert len(signature) == 64  # SHA256 hex
    
    @patch('requests.request')
    def test_make_request_get(self, mock_request, api):
        """Testa requisição GET"""
        mock_response = Mock()
        mock_response.json.return_value = {"success": True}
        mock_request.return_value = mock_response
        
        result = api._make_request("GET", "/test")
        
        assert result == {"success": True}
        mock_request.assert_called_once()
        
        # Verificar headers
        call_args = mock_request.call_args
        headers = call_args[1]['headers']
        assert "MB-ACCESS-KEY" in headers
        assert "MB-ACCESS-TIMESTAMP" in headers
        assert "MB-ACCESS-SIGNATURE" in headers
    
    @patch('requests.request')
    def test_make_request_post_with_data(self, mock_request, api):
        """Testa requisição POST com dados"""
        mock_response = Mock()
        mock_response.json.return_value = {"order_id": "123"}
        mock_request.return_value = mock_response
        
        data = {"symbol": "BTC-BRL", "side": "buy"}
        result = api._make_request("POST", "/orders", data)
        
        assert result == {"order_id": "123"}
        
        # Verificar que os dados foram enviados como JSON
        call_args = mock_request.call_args
        assert call_args[1]['data'] == json.dumps(data)
    
    @patch.object(MercadoBitcoinAPI, '_make_request')
    def test_get_account_balance(self, mock_request, api):
        """Testa obtenção de saldo"""
        mock_request.return_value = {"accounts": []}
        
        result = api.get_account_balance()
        
        mock_request.assert_called_once_with("GET", "/accounts/balance")
        assert result == {"accounts": []}
    
    @patch.object(MercadoBitcoinAPI, '_make_request')
    def test_get_ticker(self, mock_request, api):
        """Testa obtenção de ticker"""
        mock_request.return_value = {"last": "50000.00"}
        
        result = api.get_ticker("BTC-BRL")
        
        mock_request.assert_called_once_with("GET", "/ticker?symbols=BTC-BRL")
        assert result == {"last": "50000.00"}
    
    @patch.object(MercadoBitcoinAPI, '_make_request')
    def test_place_order_market(self, mock_request, api):
        """Testa colocação de ordem market"""
        mock_request.return_value = {"id": "12345"}
        
        result = api.place_order("BTC-BRL", "buy", "market", "0.001")
        
        expected_data = {
            "symbol": "BTC-BRL",
            "side": "buy",
            "type": "market",
            "quantity": "0.001"
        }
        mock_request.assert_called_once_with("POST", "/orders", expected_data)
    
    @patch.object(MercadoBitcoinAPI, '_make_request')
    def test_place_order_limit(self, mock_request, api):
        """Testa colocação de ordem limit"""
        mock_request.return_value = {"id": "12345"}
        
        result = api.place_order("BTC-BRL", "sell", "limit", "0.001", "50000.00")
        
        expected_data = {
            "symbol": "BTC-BRL",
            "side": "sell",
            "type": "limit",
            "quantity": "0.001",
            "price": "50000.00"
        }
        mock_request.assert_called_once_with("POST", "/orders", expected_data)
    
    @patch.object(MercadoBitcoinAPI, '_make_request')
    def test_get_orders_no_params(self, mock_request, api):
        """Testa listagem de ordens sem parâmetros"""
        mock_request.return_value = {"orders": []}
        
        result = api.get_orders()
        
        mock_request.assert_called_once_with("GET", "/orders")
    
    @patch.object(MercadoBitcoinAPI, '_make_request')
    def test_get_orders_with_params(self, mock_request, api):
        """Testa listagem de ordens com parâmetros"""
        mock_request.return_value = {"orders": []}
        
        result = api.get_orders(symbol="BTC-BRL", status="open")
        
        mock_request.assert_called_once_with("GET", "/orders?symbol=BTC-BRL&status=open")