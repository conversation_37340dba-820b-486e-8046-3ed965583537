import requests
import hmac
import hashlib
import time
import json
from typing import Dict, Any, <PERSON><PERSON>, List
from dataclasses import dataclass
from decimal import Decimal


@dataclass
class AccountData:
    """Representa os dados de uma conta do Mercado Bitcoin"""
    currency: str
    currencySign: str
    id: str
    name: str
    type: str

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AccountData':
        """Cria uma instância AccountData a partir de um dicionário"""
        return cls(
            currency=data['currency'],
            currencySign=data['currencySign'],
            id=data['id'],
            name=data['name'],
            type=data['type']
        )


@dataclass
class AccountBalanceData:
    """Representa os dados de saldo de uma conta do Mercado Bitcoin"""
    available: Decimal
    on_hold: Decimal
    symbol: str
    total: Decimal

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AccountBalanceData':
        """Cria uma instância AccountBalanceData a partir de um dicionário"""
        return cls(
            available=Decimal(data['available']),
            on_hold=Decimal(data['on_hold']),
            symbol=data['symbol'],
            total=Decimal(data['total'])
        )


@dataclass
class TickerData:
    """Representa os dados de um ticker do Mercado Bitcoin"""
    buy: Decimal
    date: int
    high: Decimal
    last: Decimal
    low: Decimal
    open: Decimal
    pair: str
    sell: Decimal
    vol: Decimal

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TickerData':
        """Cria uma instância TickerData a partir de um dicionário"""
        return cls(
            buy=Decimal(data['buy']),
            date=data['date'],
            high=Decimal(data['high']),
            last=Decimal(data['last']),
            low=Decimal(data['low']),
            open=Decimal(data['open']),
            pair=data['pair'],
            sell=Decimal(data['sell']),
            vol=Decimal(data['vol'])
        )


class MercadoBitcoinAPI:
    def __init__(self, api_key: str, api_secret: str):
        self.base_url = "https://api.mercadobitcoin.net/api/v4"
        self.session = requests.Session()
        self._authorize(self.session, api_key, api_secret)

    def _authorize(self, session, login: str, password: str):
        endpoint = "/authorize"
        url = f"{self.base_url}{endpoint}"

        headers = {
            "Content-Type": "application/json",
        }

        body = json.dumps({"login": login, "password": password})
        response = session.request("POST", url, headers=headers, data=body)

        if response.status_code != 200:
            raise Exception(f"Erro na requisição: {response.text}, {response.status_code}")

        json_response = response.json()
        access_token = json_response["access_token"]
        session.headers.update({"Authorization": f"Bearer {access_token}"})

    def _make_request(
        self, method: str, endpoint: str, data: Dict | None = None
    ) -> Dict[str, Any]:
        """Faz requisição autenticada para a API"""
        url = f"{self.base_url}{endpoint}"
        body = json.dumps(data) if data else ""

        headers = {
            "Content-Type": "application/json",
        }
        print("request: ", url, body)
        response = self.session.request(method, url, headers=headers, data=body)
        # print("response: ", response.text)
        if response.status_code != 200:
            raise Exception(f"Erro na requisição: {response.text}, {response.status_code}")
        return response.json()

    def get_accounts(self) -> List[AccountData]:
        """Obtém lista de contas"""
        response = self._make_request("GET", "/accounts")
        return [AccountData.from_dict(account) for account in response]

    def get_account_balance(self, account_id: str) -> List[AccountBalanceData]:
        """Obtém saldo da conta"""
        response = self._make_request("GET", f"/accounts/{account_id}/balances")
        return [AccountBalanceData.from_dict(balance) for balance in response]

    def get_ticker(self, symbol: str) -> List[TickerData]:
        """Obtém ticker de um par específico"""
        response = self._make_request("GET", f"/tickers?symbols={symbol}")
        return [TickerData.from_dict(ticker) for ticker in response][0]

    def place_order(
        self, account_id: str, symbol: str, side: str, type_order: str, quantity: str
    ) -> str:
        data = {
            "qty": quantity,
            "side": side,
            "type": type_order,
            # "async": true,
            # "cost": 100,
            # "externalId": "*********",
            # "limitPrice": 9997,
            # "stopPrice": 1000,
        }
        response = self._make_request("POST", f"/accounts/{account_id}/{symbol}/orders", data)
        # response = {"orderId": str(_get_fake_order_id())}
        return response["orderId"]



    def get_orders(self, symbol: str | None = None, status: str | None = None) -> Dict[str, Any]:
        """Lista ordens"""
        endpoint = "/orders"
        params = []
        if symbol:
            params.append(f"symbol={symbol}")
        if status:
            params.append(f"status={status}")
        if params:
            endpoint += "?" + "&".join(params)

        return self._make_request("GET", endpoint)

global counter
counter = 0
def _get_fake_order_id():
    global counter
    counter = counter + 1
    return counter