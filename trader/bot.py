import time
import logging
from typing import Dict, Any
from .mercado_bitcoin_api import MercadoBitcoinAPI
from .trading_strategy import TradingStrategy
from decimal import Decimal


class TradingBot:
    def __init__(
        self, api: MercadoBitcoinAPI, strategy: TradingStrategy, symbol: str = "BTC-BRL"
    ):
        self.api = api
        self.strategy = strategy
        self.symbol = symbol
        self.is_running = False
        self.position = None  # "long", "short", ou None
        self.position_price = Decimal("0.0")

        # Configurar logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

    def get_current_price(self) -> Decimal:
        """Obtém preço atual do par"""
        ticker: TickerData = self.api.get_ticker(self.symbol)
        return Decimal(ticker.last)

    def get_account(self, currency: str = "BRL1"):
        """Obtém saldo de uma moeda"""
        # if currency == "BRL1":
            # return "brl-account-id", Decimal("100.0")
        accounts = self.api.get_accounts()
        for account in accounts:
            balances = self.api.get_account_balance(account.id)
            for balance in balances:
                if balance.symbol == currency:
                    return account.id, balance.available
        return None, Decimal("0.0")

    def execute_buy_order(self, price: Decimal):
        """Executa ordem de compra"""
        account_id, balance = self.get_account("BRL1")
        # if balance < 50:  # Mínimo para operar
        #     self.logger.warning("Saldo insuficiente para compra")
        #     return

        quantity = self.strategy.calculate_quantity(balance, price)

        try:
            order = self.api.place_order(
                account_id=account_id,
                symbol=self.symbol, side="buy", type_order="market", quantity=quantity
            )
            self.position_price = price
            self.position = "long"
        except Exception as e:
            self.logger.error(f"Erro ao executar compra: {e}")

    def execute_sell_order(self):
        """Executa ordem de venda"""
        account_id, btc_balance = self.get_account("BTC")
        # if btc_balance < 0.00001:  # Mínimo para vender
        #     self.logger.warning("Sem BTC para vender")
        #     return

        try:
            order = self.api.place_order(
                account_id=account_id,
                symbol=self.symbol,
                side="sell",
                type_order="market",
                quantity=f"{btc_balance:.8f}",
            )
            self.position = None
        except Exception as e:
            self.logger.error(f"Erro ao executar venda: {e}")

    def run(self, interval: int = 60):
        self.profit_balance = Decimal("0.0")
        self.is_running = True
        _, brl_balance = self.get_account("BRL1")
        _, btc_balance = self.get_account("BTC")
        self.logger.info(f"Bot iniciado para {self.symbol}. Saldo para operar: BRL={brl_balance:.2f}, BTC={btc_balance:.8f}")

        while self.is_running:
            try:
                current_price = self.get_current_price()
                self.logger.info(f"Preço atual: R$ {current_price}")

                self.strategy.update_price_history(current_price)
                market_data = {"price": current_price}

                if self.position is None and self.strategy.should_buy(market_data):
                    self.logger.info("Sinal de compra detectado")
                    self.execute_buy_order(current_price)
                    self.logger.info(f"Ordem de compra executada. Preço: {current_price:.2f}")
                elif self.position == "long" and self.strategy.should_sell(market_data):
                    self.logger.info("Sinal de venda detectado")
                    self.execute_sell_order()
                    profit = current_price - self.position_price
                    self.profit_balance += profit
                    self.logger.info(f"Ordem de venda executada. Preço: {current_price:.2f}. posição: {self.position_price:.2f}. Lucro: {profit:.2f}")
                self.logger.info(f"Lucro acumulado: {self.profit_balance:.2f}")

                time.sleep(interval)

            except KeyboardInterrupt:
                self.logger.info("Bot interrompido pelo usuário")
                self.stop()
            except Exception as e:
                self.logger.error(f"Erro no loop principal: {e}")
                time.sleep(interval)

    def stop(self):
        """Para o bot"""
        self.is_running = False
        self.logger.info("Bot parado")
