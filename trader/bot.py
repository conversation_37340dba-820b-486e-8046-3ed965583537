import time
import logging
from typing import Dict, Any, List, Optional
from .mercado_bitcoin_api import MercadoBitcoinAPI
from .trading_strategy import TradingStrategy
from .account import Account
from .colored_logger import get_trading_logger, TradingLogger
from decimal import Decimal
from dataclasses import dataclass
from datetime import datetime


class TradingBot:
    def __init__(
        self, api: MercadoBitcoinAPI, strategy: TradingStrategy, account: Account
    ):
        self.api = api
        self.strategy = strategy
        self.symbol = account.symbol
        self.is_running = False
        self.account = account

        # Configurar logging colorido
        self.trading_logger = get_trading_logger("TradingBot")
        self.logger = self.trading_logger.get_logger()

    def get_current_price(self) -> Decimal:
        """Obtém preço atual do par"""
        ticker = self.api.get_ticker(self.symbol)
        return Decimal(ticker.last)

    def run(self, interval: int = 60):
        self.is_running = True

        self.trading_logger.log_bot_start(self.symbol)

        while self.is_running:
            try:
                current_price = self.get_current_price()
                self.trading_logger.log_price(self.symbol, float(current_price))

                # Atualizar preço da posição atual
                self.account.update_position_price(current_price)

                self.strategy.update_price_history(current_price)
                market_data = {"price": current_price}

                # Verificar se a estratégia indica compra e se é possível comprar
                if self.account.can_buy() and self.strategy.should_buy(market_data):
                    self.trading_logger.log_buy_signal(float(current_price))
                    success = self.account.execute_buy_order(current_price, self.strategy.calculate_quantity)
                    if success:
                        position = self.account.get_position()
                        if position:
                            self.trading_logger.log_buy_order(float(current_price), float(position.quantity))

                # Verificar se a estratégia indica venda e se é possível vender
                elif self.account.can_sell() and self.strategy.should_sell(market_data):
                    position = self.account.get_position()
                    old_quantity = float(position.quantity) if position else 0.0

                    self.trading_logger.log_sell_signal(float(current_price))
                    success = self.account.execute_sell_order()
                    if success:
                        self.trading_logger.log_sell_order(float(current_price), old_quantity)

                # Log de informações da conta
                position = self.account.get_position()
                if position:
                    self.trading_logger.log_position(position.side, float(position.quantity), float(position.entry_price))

                # Log de PnL
                unrealized_pnl = self.account.get_unrealized_pnl()
                total_pnl = self.account.get_total_realized_pnl()
                self.trading_logger.log_pnl(float(unrealized_pnl), float(total_pnl))

                time.sleep(interval)

            except KeyboardInterrupt:
                self.logger.info("🛑 Bot interrompido pelo usuário")
                self.stop()
            except Exception as e:
                self.trading_logger.log_error("Erro no loop principal", e)
                time.sleep(interval)

    def show_execution_report(self):
        """Mostra relatório de execução"""
        self.logger.info("📊 ===== RELATÓRIO DE EXECUÇÃO =====")

        realized_pnl = self.account.get_total_realized_pnl()
        unrealized_pnl = self.account.get_unrealized_pnl()

        self.trading_logger.log_pnl(float(unrealized_pnl), float(realized_pnl))

        if len(self.strategy.price_history) > 1:
            price_variation = self.strategy.price_history[-1] - self.strategy.price_history[0]
            self.logger.info(f"📈 Variação do preço: R$ {price_variation:.2f}")

        # Mostrar histórico de posições
        if self.account.position_history:
            self.logger.info(f"📋 Total de operações realizadas: {len(self.account.position_history)}")
            profitable_trades = sum(1 for pos in self.account.position_history if pos.realized_pnl > 0)
            self.logger.info(f"✅ Operações lucrativas: {profitable_trades}/{len(self.account.position_history)}")

        self.logger.info("📊 ===========================")

    def stop(self):
        """Para o bot"""
        self.is_running = False
        self.trading_logger.log_bot_stop()
        self.show_execution_report()
