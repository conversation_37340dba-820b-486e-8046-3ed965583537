import time
import logging
from typing import Dict, Any, List, Optional
from .mercado_bitcoin_api import MercadoBitcoinAPI
from .trading_strategy import TradingStrategy
from .account import Account
from decimal import Decimal
from dataclasses import dataclass
from datetime import datetime


class TradingBot:
    def __init__(
        self, api: MercadoBitcoinAPI, strategy: TradingStrategy, account: Account
    ):
        self.api = api
        self.strategy = strategy
        self.symbol = account.symbol
        self.is_running = False
        self.account = account

        # Configurar logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

    def get_current_price(self) -> Decimal:
        """Obtém preço atual do par"""
        ticker = self.api.get_ticker(self.symbol)
        return Decimal(ticker.last)

    def run(self, interval: int = 60):
        self.is_running = True
        self.logger.info(f"Bot iniciado para {self.symbol}")

        while self.is_running:
            try:
                current_price = self.get_current_price()
                self.logger.info(f"Preço atual: R$ {current_price}")

                # Atualizar preço da posição atual
                self.account.update_position_price(current_price)

                self.strategy.update_price_history(current_price)
                market_data = {"price": current_price}

                # Verificar se a estratégia indica compra e se é possível comprar
                if self.account.can_buy() and self.strategy.should_buy(market_data):
                    self.logger.info("Sinal de compra detectado")
                    success = self.account.execute_buy_order(current_price, self.strategy.calculate_quantity)
                    if success:
                        self.logger.info(f"Ordem de compra executada. Preço: {current_price:.2f}")

                # Verificar se a estratégia indica venda e se é possível vender
                elif self.account.can_sell() and self.strategy.should_sell(market_data):
                    self.logger.info("Sinal de venda detectado")
                    success = self.account.execute_sell_order()
                    if success:
                        self.logger.info(f"Ordem de venda executada. Preço: {current_price:.2f}")

                # Log de informações da conta
                position = self.account.get_position()
                if position:
                    self.logger.info(f"Posição atual: {position.side} {position.quantity:.8f} @ {position.entry_price:.2f}")
                    self.logger.info(f"PnL não realizado: {self.account.get_unrealized_pnl():.2f}")

                total_pnl = self.account.get_total_realized_pnl()
                self.logger.info(f"PnL total realizado: {total_pnl:.2f}")

                time.sleep(interval)

            except KeyboardInterrupt:
                self.logger.info("Bot interrompido pelo usuário")
                self.stop()
            except Exception as e:
                self.logger.error(f"Erro no loop principal: {e}")
                time.sleep(interval)

    def show_execution_report(self):
        """Mostra relatório de execução"""
        self.logger.info("Relatório de Execução")
        self.logger.info(f"Total de PnL Realizado: {self.account.get_total_realized_pnl():.2f}")
        self.logger.info(f"Total de PnL Não Realizado: {self.account.get_unrealized_pnl():.2f}")
        self.logger.info(f"Variação do preço: {self.strategy.price_history[-1] - self.strategy.price_history[0]}")

    def stop(self):
        """Para o bot"""
        self.is_running = False
        self.logger.info("Bot parado")
        self.show_execution_report()
